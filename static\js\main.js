// Main JavaScript file for AI Agent System

// Global variables
window.API_BASE = '/api/v1';
window.ANONYMOUS_TOKEN = null;

// Utility functions
function showAlert(message, type = 'info', duration = 5000) {
    const alertContainer = document.getElementById('alertContainer') || createAlertContainer();
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertDiv);
    
    // Auto-dismiss after duration
    if (duration > 0) {
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, duration);
    }
}

function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alertContainer';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1050';
    document.body.appendChild(container);
    return container;
}

let loadingStack = 0;
let loadingTimeout = null;

function showLoading(show = true, message = 'Loading...') {
    const modal = document.getElementById('loadingModal');
    if (!modal) {
        console.warn('Loading modal not found');
        return;
    }

    const messageElement = modal.querySelector('p');
    if (messageElement) {
        messageElement.textContent = message;
    }

    if (show) {
        loadingStack++;
        console.log(`Loading stack increased to: ${loadingStack}`);
        if (loadingStack === 1) {
            try {
                let modalInstance = bootstrap.Modal.getInstance(modal);
                if (!modalInstance) {
                    modalInstance = new bootstrap.Modal(modal, {
                        backdrop: 'static',
                        keyboard: false,
                        focus: false
                    });
                }
                modalInstance.show();
                console.log('Loading modal shown');

                // Set a safety timeout to force close if stuck
                if (loadingTimeout) {
                    clearTimeout(loadingTimeout);
                }
                loadingTimeout = setTimeout(() => {
                    console.warn('Loading modal timeout reached, force closing...');
                    forceCloseLoading();
                }, 30000); // 30 second timeout

            } catch (error) {
                console.error('Error showing loading modal:', error);
                // Reset stack on error
                loadingStack = 0;
            }
        }
    } else {
        loadingStack--;
        console.log(`Loading stack decreased to: ${loadingStack}`);
        if (loadingStack <= 0) {
            loadingStack = 0; // Reset to 0 in case of multiple calls

            // Clear the safety timeout
            if (loadingTimeout) {
                clearTimeout(loadingTimeout);
                loadingTimeout = null;
            }

            try {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                    console.log('Loading modal hidden');
                } else {
                    console.warn('No modal instance found when trying to hide');
                    // Force close as fallback
                    forceCloseLoading();
                }
            } catch (error) {
                console.error('Error hiding loading modal:', error);
                forceCloseLoading();
            }
        }
    }
}

// Force close loading modal (utility function)
function forceCloseLoading() {
    console.log('Force closing loading modal...');

    // Reset loading stack
    loadingStack = 0;

    const modal = document.getElementById('loadingModal');
    if (modal) {
        try {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
                setTimeout(() => {
                    try {
                        modalInstance.dispose();
                    } catch (error) {
                        console.warn('Error disposing modal instance during force close:', error);
                    }
                }, 100);
            }
        } catch (error) {
            console.warn('Error with Bootstrap modal instance during force close:', error);
        }

        // Direct DOM manipulation as fallback
        modal.style.display = 'none';
        modal.classList.remove('show');
        modal.setAttribute('aria-hidden', 'true');
        modal.removeAttribute('aria-modal');

        // Clean up body classes and backdrop
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('overflow');
        document.body.style.removeProperty('padding-right');

        // Remove all modal backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
    }

    // Clear timeout
    if (loadingTimeout) {
        clearTimeout(loadingTimeout);
        loadingTimeout = null;
    }

    console.log('Loading modal force closed, stack reset to 0');
}

// Anonymous authentication functions
async function getAnonymousToken() {
    if (window.ANONYMOUS_TOKEN) {
        return window.ANONYMOUS_TOKEN;
    }

    try {
        const response = await fetch(`${API_BASE}/auth/anonymous`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (!response.ok) {
            throw new Error('Failed to get anonymous token');
        }

        const data = await response.json();
        window.ANONYMOUS_TOKEN = data.access_token;

        // Store token in localStorage for persistence
        localStorage.setItem('anonymous_token', data.access_token);

        return data.access_token;
    } catch (error) {
        console.error('Error getting anonymous token:', error);
        throw error;
    }
}

async function ensureAuthentication() {
    // Check if we have a token in localStorage
    const storedToken = localStorage.getItem('anonymous_token');
    if (storedToken) {
        window.ANONYMOUS_TOKEN = storedToken;
        return storedToken;
    }

    // Get new anonymous token
    return await getAnonymousToken();
}

// API helper functions
async function apiRequest(url, options = {}) {
    // Ensure we have an authentication token
    await ensureAuthentication();

    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${window.ANONYMOUS_TOKEN}`,
        },
    };

    const mergedOptions = { ...defaultOptions, ...options };

    // Merge headers properly
    if (options.headers) {
        mergedOptions.headers = { ...defaultOptions.headers, ...options.headers };
    }

    try {
        // Add timeout to prevent hanging requests
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        const response = await fetch(`${API_BASE}${url}`, {
            ...mergedOptions,
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            // If unauthorized, try to get a new token and retry once
            if (response.status === 401) {
                console.log('Token expired, getting new anonymous token...');
                localStorage.removeItem('anonymous_token');
                window.ANONYMOUS_TOKEN = null;

                try {
                    await ensureAuthentication();
                    // Update authorization header and retry
                    mergedOptions.headers['Authorization'] = `Bearer ${window.ANONYMOUS_TOKEN}`;

                    const retryResponse = await fetch(`${API_BASE}${url}`, {
                        ...mergedOptions,
                        signal: controller.signal
                    });

                    if (!retryResponse.ok) {
                        const errorData = await retryResponse.json().catch(() => ({}));
                        throw new Error(errorData.detail || `HTTP ${retryResponse.status}: ${retryResponse.statusText}`);
                    }

                    return await retryResponse.json();
                } catch (retryError) {
                    console.error('Retry failed:', retryError);
                    throw retryError;
                }
            }

            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        if (error.name === 'AbortError') {
            throw new Error('Request timeout - please try again');
        }
        console.error('API Request failed:', error);
        throw error;
    }
}

async function apiGet(url) {
    return apiRequest(url, { method: 'GET' });
}

async function apiPost(url, data) {
    return apiRequest(url, {
        method: 'POST',
        body: JSON.stringify(data),
    });
}

async function apiPut(url, data) {
    return apiRequest(url, {
        method: 'PUT',
        body: JSON.stringify(data),
    });
}

async function apiDelete(url) {
    return apiRequest(url, { method: 'DELETE' });
}

// Form validation helpers
function validateForm(formElement) {
    const inputs = formElement.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

function clearFormValidation(formElement) {
    const inputs = formElement.querySelectorAll('.is-invalid');
    inputs.forEach(input => {
        input.classList.remove('is-invalid');
    });
}

// Date formatting
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
}

function formatRelativeTime(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return formatDate(dateString);
}

// Text utilities
function truncateText(text, maxLength = 100) {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Local storage helpers
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
        console.warn('Failed to save to localStorage:', error);
    }
}

function loadFromLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.warn('Failed to load from localStorage:', error);
        return defaultValue;
    }
}

// Initialize tooltips and popovers
document.addEventListener('DOMContentLoaded', function() {
    // Initialize anonymous authentication
    ensureAuthentication().catch(error => {
        console.error('Failed to initialize anonymous authentication:', error);
        showAlert('Failed to initialize authentication. Some features may not work.', 'warning');
    });

    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize Bootstrap popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }

    // Set up periodic check for stuck loading modals
    setInterval(() => {
        const modal = document.getElementById('loadingModal');
        if (modal && modal.classList.contains('show') && loadingStack === 0) {
            console.warn('Detected stuck loading modal, force closing...');
            forceCloseLoading();
        }
    }, 5000); // Check every 5 seconds
});

// Handle navigation active states
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
        }
    });
});

// Global error handler
window.addEventListener('error', function(event) {
    console.error('Global error:', event.error);

    // Handle Bootstrap modal focus errors specifically
    if (event.message && event.message.includes('Cannot read properties of null (reading \'focus\')')) {
        console.warn('Bootstrap modal focus error detected - attempting to fix');
        // Try to force close any stuck loading modals
        setTimeout(() => {
            forceCloseLoading();
        }, 100);
        return; // Don't show alert for this specific error
    }

    // Don't show alerts for common script errors that don't affect functionality
    const ignoredErrors = [
        'Script error',
        'Non-Error promise rejection captured',
        'ResizeObserver loop limit exceeded',
        'Cannot read properties of null (reading \'focus\')'
    ];

    if (!ignoredErrors.some(ignored => event.message && event.message.includes(ignored))) {
        showAlert('An unexpected error occurred. Please try again.', 'danger');
    }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);

    // Don't show alerts for common promise rejections
    const ignoredRejections = [
        'AbortError',
        'The user aborted a request'
    ];

    if (!ignoredRejections.some(ignored => event.reason && event.reason.toString().includes(ignored))) {
        showAlert('An unexpected error occurred. Please try again.', 'danger');
    }
});

// Debug function to check loading modal state
function debugLoadingModal() {
    const modal = document.getElementById('loadingModal');
    const modalInstance = bootstrap.Modal.getInstance(modal);

    console.log('=== LOADING MODAL DEBUG ===');
    console.log('Loading stack:', loadingStack);
    console.log('Loading timeout:', loadingTimeout);
    console.log('Modal element:', modal);
    console.log('Modal instance:', modalInstance);
    console.log('Modal visible:', modal ? modal.classList.contains('show') : 'N/A');
    console.log('Body modal-open:', document.body.classList.contains('modal-open'));
    console.log('Backdrops:', document.querySelectorAll('.modal-backdrop').length);
    console.log('========================');
}

// Export functions for use in other scripts
window.utils = {
    showAlert,
    showLoading,
    forceCloseLoading,
    debugLoadingModal,
    apiGet,
    apiPost,
    apiPut,
    apiDelete,
    validateForm,
    clearFormValidation,
    formatDate,
    formatRelativeTime,
    truncateText,
    escapeHtml,
    saveToLocalStorage,
    loadFromLocalStorage,
    getAnonymousToken,
    ensureAuthentication
};

// Add CSRF token support if needed
function getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : null;
}
